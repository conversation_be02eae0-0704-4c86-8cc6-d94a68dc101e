import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Load data
df = pd.read_csv('comprehensive_crypto_data.csv')
df['date'] = pd.to_datetime(df['date'])

print("=" * 100)
print("HNT VIABILITY ASSESSMENT: VOLUME, TECHNICAL ANALYSIS & MINING OUTLOOK")
print("=" * 100)

# Focus on HNT data
hnt_data = df[df['symbol'] == 'HNT'].copy()
hnt_data = hnt_data.sort_values('date')

# 1. Volume Analysis
print("\n" + "=" * 100)
print("1. VOLUME ANALYSIS - LIQUIDITY AND INTEREST TRENDS")
print("=" * 100)

# Calculate volume statistics
volume_stats = hnt_data['volume_usd'].describe()
print(f"Volume Statistics (USD):")
print(f"  Average Daily Volume: ${volume_stats['mean']:,.0f}")
print(f"  Median Daily Volume: ${volume_stats['50%']:,.0f}")
print(f"  Maximum Daily Volume: ${volume_stats['max']:,.0f}")
print(f"  Minimum Daily Volume: ${volume_stats['min']:,.0f}")

# Volume trends over time
hnt_data['volume_ma_30'] = hnt_data['volume_usd'].rolling(30).mean()
hnt_data['volume_ma_90'] = hnt_data['volume_usd'].rolling(90).mean()

recent_volume = hnt_data['volume_usd'].tail(30).mean()
early_volume = hnt_data['volume_usd'].head(90).mean()
volume_change = ((recent_volume - early_volume) / early_volume) * 100

print(f"\nVolume Trends:")
print(f"  Early Period Avg Volume (first 90 days): ${early_volume:,.0f}")
print(f"  Recent Period Avg Volume (last 30 days): ${recent_volume:,.0f}")
print(f"  Volume Change: {volume_change:+.1f}%")

# Volume-Price relationship
volume_price_corr = hnt_data['volume_usd'].corr(hnt_data['close'])
print(f"  Volume-Price Correlation: {volume_price_corr:.3f}")

# 2. Technical Analysis
print("\n" + "=" * 100)
print("2. TECHNICAL ANALYSIS - TREND AND MOMENTUM INDICATORS")
print("=" * 100)

# Current technical indicators
latest = hnt_data.iloc[-1]
print(f"Current Technical Indicators (as of {latest['date'].date()}):")
print(f"  Price: ${latest['close']:.4f}")
print(f"  RSI (14-day): {latest['rsi_14']:.1f}")
print(f"  MACD: {latest['macd']:.6f}")
print(f"  MACD Signal: {latest['macd_signal']:.6f}")
print(f"  MACD Histogram: {latest['macd_histogram']:.6f}")

# RSI analysis
rsi_oversold = (hnt_data['rsi_14'] < 30).sum()
rsi_overbought = (hnt_data['rsi_14'] > 70).sum()
rsi_neutral = ((hnt_data['rsi_14'] >= 30) & (hnt_data['rsi_14'] <= 70)).sum()

print(f"\nRSI Distribution:")
print(f"  Oversold periods (RSI < 30): {rsi_oversold} days ({rsi_oversold/len(hnt_data)*100:.1f}%)")
print(f"  Overbought periods (RSI > 70): {rsi_overbought} days ({rsi_overbought/len(hnt_data)*100:.1f}%)")
print(f"  Neutral periods (30-70): {rsi_neutral} days ({rsi_neutral/len(hnt_data)*100:.1f}%)")

# Moving averages analysis
current_price = latest['close']
sma_7 = latest['sma_7']
sma_30 = latest['sma_30']
sma_90 = latest['sma_90']

print(f"\nMoving Averages Analysis:")
print(f"  Current Price vs SMA-7: {((current_price/sma_7-1)*100):+.1f}%")
print(f"  Current Price vs SMA-30: {((current_price/sma_30-1)*100):+.1f}%")
print(f"  Current Price vs SMA-90: {((current_price/sma_90-1)*100):+.1f}%")

# Trend analysis
if current_price > sma_7 > sma_30 > sma_90:
    trend = "Strong Uptrend"
elif current_price > sma_7 > sma_30:
    trend = "Moderate Uptrend"
elif current_price < sma_7 < sma_30 < sma_90:
    trend = "Strong Downtrend"
elif current_price < sma_7 < sma_30:
    trend = "Moderate Downtrend"
else:
    trend = "Sideways/Mixed"

print(f"  Current Trend: {trend}")

# 3. Volatility Analysis
print("\n" + "=" * 100)
print("3. VOLATILITY ANALYSIS - RISK ASSESSMENT")
print("=" * 100)

# Calculate different volatility measures
hnt_data['volatility_7d'] = hnt_data['daily_return'].rolling(7).std() * np.sqrt(365)
hnt_data['volatility_30d'] = hnt_data['daily_return'].rolling(30).std() * np.sqrt(365)
hnt_data['volatility_90d'] = hnt_data['daily_return'].rolling(90).std() * np.sqrt(365)

current_vol_7d = hnt_data['volatility_7d'].iloc[-1]
current_vol_30d = hnt_data['volatility_30d'].iloc[-1]
current_vol_90d = hnt_data['volatility_90d'].iloc[-1]
avg_vol_30d = hnt_data['volatility_30d'].mean()

print(f"Volatility Metrics:")
print(f"  Current 7-day volatility: {current_vol_7d:.1f}%")
print(f"  Current 30-day volatility: {current_vol_30d:.1f}%")
print(f"  Current 90-day volatility: {current_vol_90d:.1f}%")
print(f"  Average 30-day volatility: {avg_vol_30d:.1f}%")

# Compare with major cryptos
btc_data = df[df['symbol'] == 'BTC']
eth_data = df[df['symbol'] == 'ETH']
btc_vol = btc_data['daily_return'].std() * np.sqrt(365)
eth_vol = eth_data['daily_return'].std() * np.sqrt(365)

print(f"\nVolatility Comparison:")
print(f"  HNT Annual Volatility: {hnt_data['daily_return'].std() * np.sqrt(365):.1f}%")
print(f"  BTC Annual Volatility: {btc_vol:.1f}%")
print(f"  ETH Annual Volatility: {eth_vol:.1f}%")

# 4. Mining Viability Assessment
print("\n" + "=" * 100)
print("4. MINING VIABILITY ASSESSMENT")
print("=" * 100)

# Price trend analysis for mining viability
price_6m_ago = hnt_data['close'].iloc[-180] if len(hnt_data) >= 180 else hnt_data['close'].iloc[0]
price_1y_ago = hnt_data['close'].iloc[-365] if len(hnt_data) >= 365 else hnt_data['close'].iloc[0]
current_price = hnt_data['close'].iloc[-1]

print(f"Price Trend Analysis:")
print(f"  Current Price: ${current_price:.4f}")
print(f"  6 months ago: ${price_6m_ago:.4f} ({((current_price/price_6m_ago-1)*100):+.1f}%)")
print(f"  1 year ago: ${price_1y_ago:.4f} ({((current_price/price_1y_ago-1)*100):+.1f}%)")

# Volume sustainability
recent_avg_volume = hnt_data['volume_usd'].tail(90).mean()
print(f"\nLiquidity Assessment:")
print(f"  Recent 90-day avg volume: ${recent_avg_volume:,.0f}")
if recent_avg_volume > 1000000:
    liquidity_status = "Good - Sufficient for mining operations"
elif recent_avg_volume > 500000:
    liquidity_status = "Moderate - May face some liquidity constraints"
else:
    liquidity_status = "Poor - Significant liquidity concerns"
print(f"  Liquidity Status: {liquidity_status}")

# 5. Final Viability Assessment
print("\n" + "=" * 100)
print("5. FINAL VIABILITY ASSESSMENT")
print("=" * 100)

# Calculate key metrics for assessment
total_return_3y = ((current_price / hnt_data['close'].iloc[0]) - 1) * 100
sharpe_ratio = hnt_data['daily_return'].mean() / hnt_data['daily_return'].std() * np.sqrt(365)
max_drawdown = ((hnt_data['close'].min() / hnt_data['close'].max()) - 1) * 100
current_from_ath = ((current_price / hnt_data['close'].max()) - 1) * 100

print(f"Key Performance Metrics:")
print(f"  3-Year Total Return: {total_return_3y:.1f}%")
print(f"  Sharpe Ratio: {sharpe_ratio:.2f}")
print(f"  Maximum Drawdown: {max_drawdown:.1f}%")
print(f"  Current Distance from ATH: {current_from_ath:.1f}%")
print(f"  Current RSI: {latest['rsi_14']:.1f}")
print(f"  Current Trend: {trend}")

# Mining viability score
viability_score = 0
viability_factors = []

# Price performance (weight: 30%)
if total_return_3y > 0:
    viability_score += 30
    viability_factors.append("✓ Positive 3-year return")
elif total_return_3y > -50:
    viability_score += 15
    viability_factors.append("⚠ Moderate negative return (-50% to 0%)")
else:
    viability_factors.append("✗ Severe negative return (>-50%)")

# Liquidity (weight: 25%)
if recent_avg_volume > 1000000:
    viability_score += 25
    viability_factors.append("✓ Strong liquidity (>$1M daily)")
elif recent_avg_volume > 500000:
    viability_score += 15
    viability_factors.append("⚠ Moderate liquidity ($500K-$1M daily)")
else:
    viability_factors.append("✗ Poor liquidity (<$500K daily)")

# Volatility (weight: 20%)
hnt_annual_vol = hnt_data['daily_return'].std() * np.sqrt(365)
if hnt_annual_vol < 50:
    viability_score += 20
    viability_factors.append("✓ Reasonable volatility (<50%)")
elif hnt_annual_vol < 80:
    viability_score += 10
    viability_factors.append("⚠ High volatility (50-80%)")
else:
    viability_factors.append("✗ Extreme volatility (>80%)")

# Technical indicators (weight: 15%)
if latest['rsi_14'] < 30:
    viability_score += 15
    viability_factors.append("✓ Oversold - potential upside")
elif latest['rsi_14'] > 70:
    viability_score += 5
    viability_factors.append("⚠ Overbought - limited upside")
else:
    viability_score += 10
    viability_factors.append("⚠ Neutral RSI")

# Correlation with major cryptos (weight: 10%)
btc_corr = df.pivot(index='date', columns='symbol', values='daily_return').corr()['HNT']['BTC']
if btc_corr > 0.3:
    viability_score += 10
    viability_factors.append("✓ Follows crypto market trends")
else:
    viability_factors.append("⚠ Disconnected from crypto market")

print(f"\nViability Assessment Factors:")
for factor in viability_factors:
    print(f"  {factor}")

print(f"\nOverall Viability Score: {viability_score}/100")

if viability_score >= 70:
    recommendation = "VIABLE - HNT mining shows strong potential"
elif viability_score >= 50:
    recommendation = "CAUTIOUSLY VIABLE - Proceed with careful risk management"
elif viability_score >= 30:
    recommendation = "QUESTIONABLE - High risk, consider alternatives"
else:
    recommendation = "NOT VIABLE - Significant concerns, avoid or exit"

print(f"Recommendation: {recommendation}")

print(f"\n" + "=" * 100)
print("SUMMARY AND CONCLUSIONS")
print("=" * 100)
print("Based on the comprehensive analysis of HNT over the 3-year period:")
print("1. Price performance has been disappointing with -40.8% total return")
print("2. HNT shows strong correlation with other DePIN tokens and moderate correlation with major cryptos")
print("3. Liquidity is moderate but sufficient for most mining operations")
print("4. Technical indicators suggest the token is currently in a neutral to bearish trend")
print("5. Volatility is high, creating both risk and opportunity")
print(f"6. Overall viability score: {viability_score}/100 - {recommendation}")
print("=" * 100)
