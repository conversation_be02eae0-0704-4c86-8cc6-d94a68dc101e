import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')

# Load data
df = pd.read_csv('C:/users/<USER>/desktop/helium_comparison/comprehensive_crypto_data.csv')
df['date'] = pd.to_datetime(df['date'])

# Token categorization
major_coins = ['BTC', 'ETH', 'SOL', 'AVAX', 'MATIC']
depin_coins = ['HNT', 'IOTX', 'AKT', 'FIL', 'THETA', 'RNDR', 'TAO']

print("=" * 100)
print("COMPREHENSIVE HNT VIABILITY ANALYSIS")
print("=" * 100)

# 1. Basic HNT Statistics
print("\n" + "=" * 100)
print("1. HNT PRICE PERFORMANCE")
print("=" * 100)
hnt_data = df[df['symbol'] == 'HNT'].copy()

print(f"Date Range: {hnt_data['date'].min().date()} to {hnt_data['date'].max().date()}")
print(f"Data Points: {len(hnt_data)}")
print(f"\nPrice Statistics:")
print(f"  Starting Price (Oct 2022): ${hnt_data['close'].iloc[0]:.4f}")
print(f"  Current Price (Oct 2025): ${hnt_data['close'].iloc[-1]:.4f}")
print(f"  All-Time High in Period: ${hnt_data['close'].max():.4f}")
print(f"  All-Time Low in Period: ${hnt_data['close'].min():.4f}")
print(f"  Overall Return: {((hnt_data['close'].iloc[-1] / hnt_data['close'].iloc[0]) - 1) * 100:.2f}%")
print(f"  Average Daily Return: {hnt_data['daily_return'].mean():.4f}%")
print(f"  Daily Volatility: {hnt_data['daily_return'].std():.4f}%")
print(f"  Sharpe Ratio (assuming 0% risk-free rate): {hnt_data['daily_return'].mean() / hnt_data['daily_return'].std() * np.sqrt(365):.4f}")

# 2. Correlation Analysis
print("\n" + "=" * 100)
print("2. CORRELATION ANALYSIS WITH OTHER CRYPTOCURRENCIES")
print("=" * 100)

price_pivot = df.pivot(index='date', columns='symbol', values='close')
returns_pivot = df.pivot(index='date', columns='symbol', values='daily_return')

price_corr = price_pivot.corr()
returns_corr = returns_pivot.corr()

print("\nPRICE CORRELATION WITH HNT (sorted by strength):")
hnt_price_corr = price_corr['HNT'].sort_values(ascending=False)
for token, corr in hnt_price_corr.items():
    if token != 'HNT':
        category = "Major L1" if token in major_coins else "DePIN"
        strength = "Very Strong" if abs(corr) > 0.7 else "Strong" if abs(corr) > 0.5 else "Moderate" if abs(corr) > 0.3 else "Weak"
        print(f"  {token:8s} [{category:10s}]: {corr:+.4f} ({strength})")

print("\nDAILY RETURNS CORRELATION WITH HNT (sorted by strength):")
hnt_returns_corr = returns_corr['HNT'].sort_values(ascending=False)
for token, corr in hnt_returns_corr.items():
    if token != 'HNT':
        category = "Major L1" if token in major_coins else "DePIN"
        strength = "Very Strong" if abs(corr) > 0.7 else "Strong" if abs(corr) > 0.5 else "Moderate" if abs(corr) > 0.3 else "Weak"
        print(f"  {token:8s} [{category:10s}]: {corr:+.4f} ({strength})")

# 3. Lead-Lag Analysis
print("\n" + "=" * 100)
print("3. LEAD-LAG ANALYSIS: DO MAJOR COINS PREDICT HNT MOVEMENTS?")
print("=" * 100)

def lead_lag_analysis(leader_series, follower_series, max_lag=14):
    results = []
    for lag in range(0, max_lag + 1):
        if lag == 0:
            corr = leader_series.corr(follower_series)
        else:
            corr = leader_series.shift(lag).corr(follower_series)
        results.append({'lag': lag, 'correlation': corr})
    return pd.DataFrame(results)

for major_coin in ['BTC', 'ETH', 'SOL']:
    lag_analysis = lead_lag_analysis(returns_pivot[major_coin], returns_pivot['HNT'], max_lag=14)
    best_lag = lag_analysis.loc[lag_analysis['correlation'].idxmax()]
    print(f"\n{major_coin} Leading HNT:")
    print(f"  Same-day correlation: {lag_analysis.loc[0, 'correlation']:.4f}")
    print(f"  Best lag: {int(best_lag['lag'])} days (correlation: {best_lag['correlation']:.4f})")
    if best_lag['lag'] == 0:
        print(f"  → HNT moves SIMULTANEOUSLY with {major_coin}")
    else:
        print(f"  → {major_coin} movements have minimal predictive power for HNT beyond same-day effects")

# 4. Extreme Movement Analysis
print("\n" + "=" * 100)
print("4. EXTREME MOVEMENT ANALYSIS")
print("=" * 100)

def analyze_extreme_movements(leader, follower, threshold_percentile=95):
    leader_extreme_up = leader > leader.quantile(threshold_percentile/100)
    leader_extreme_down = leader < leader.quantile((100-threshold_percentile)/100)
    
    return {
        'leader_up_days': leader_extreme_up.sum(),
        'leader_down_days': leader_extreme_down.sum(),
        'avg_follower_after_leader_up': follower[leader_extreme_up.shift(1).fillna(False)].mean(),
        'avg_follower_after_leader_down': follower[leader_extreme_down.shift(1).fillna(False)].mean(),
        'avg_follower_normal': follower[~(leader_extreme_up | leader_extreme_down)].mean()
    }

for major_coin in ['BTC', 'ETH', 'SOL']:
    extreme = analyze_extreme_movements(returns_pivot[major_coin], returns_pivot['HNT'])
    print(f"\nImpact of {major_coin} Extreme Movements on HNT:")
    print(f"  Days with extreme UP moves (>95th percentile): {extreme['leader_up_days']}")
    print(f"  Days with extreme DOWN moves (<5th percentile): {extreme['leader_down_days']}")
    print(f"  Avg HNT return day AFTER {major_coin} extreme UP: {extreme['avg_follower_after_leader_up']:+.4f}%")
    print(f"  Avg HNT return day AFTER {major_coin} extreme DOWN: {extreme['avg_follower_after_leader_down']:+.4f}%")
    print(f"  Avg HNT return on NORMAL days: {extreme['avg_follower_normal']:+.4f}%")

# 5. Market Phase Analysis
print("\n" + "=" * 100)
print("5. HNT PERFORMANCE ACROSS MARKET PHASES")
print("=" * 100)

price_pivot['BTC_MA_200'] = price_pivot['BTC'].rolling(window=200).mean()
price_pivot['market_phase'] = 'neutral'
price_pivot.loc[price_pivot['BTC'] > price_pivot['BTC_MA_200'] * 1.1, 'market_phase'] = 'bull'
price_pivot.loc[price_pivot['BTC'] < price_pivot['BTC_MA_200'] * 0.9, 'market_phase'] = 'bear'

for phase in ['bull', 'bear', 'neutral']:
    phase_mask = price_pivot['market_phase'] == phase
    phase_returns = returns_pivot[phase_mask]
    
    print(f"\n{phase.upper()} MARKET (BTC {phase} phase):")
    print(f"  Days in phase: {phase_mask.sum()}")
    print(f"  HNT avg daily return: {phase_returns['HNT'].mean():+.4f}%")
    print(f"  HNT volatility: {phase_returns['HNT'].std():.4f}%")
    print(f"  BTC avg daily return: {phase_returns['BTC'].mean():+.4f}%")
    print(f"  HNT outperformance vs BTC: {(phase_returns['HNT'].mean() - phase_returns['BTC'].mean()):+.4f}%")
    
    # Calculate win rate
    hnt_wins = (phase_returns['HNT'] > 0).sum()
    print(f"  HNT positive days: {hnt_wins}/{phase_mask.sum()} ({hnt_wins/phase_mask.sum()*100:.1f}%)")
