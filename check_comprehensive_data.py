import pandas as pd
import numpy as np

# Load the comprehensive dataset
df = pd.read_csv('comprehensive_crypto_data.csv')

print("🚀 COMPREHENSIVE CRYPTOCURRENCY DATASET OVERVIEW")
print("=" * 60)

print(f"📊 Dataset Size: {len(df):,} records")
print(f"🪙 Tokens: {sorted(df['symbol'].unique())}")
print(f"📅 Date Range: {df['date'].min()} to {df['date'].max()}")
print(f"📋 Total Columns: {len(df.columns)}")

print(f"\n📈 ALL AVAILABLE METRICS ({len(df.columns)} columns):")
print("-" * 60)

# Group columns by category
core_ohlcv = ['date', 'symbol', 'open', 'high', 'low', 'close', 'volume_token', 'volume_usd', 'market_cap']
performance = ['daily_return', 'price_change', 'price_change_pct', 'momentum_7d', 'momentum_30d', 'momentum_90d']
technical = ['sma_7', 'sma_30', 'sma_90', 'ema_12', 'ema_26', 'macd', 'macd_signal', 'macd_histogram', 'rsi_14']
bollinger = ['bb_upper', 'bb_lower', 'bb_middle', 'bb_width', 'bb_position']
risk_volatility = ['volatility_30d', 'volatility_90d', 'high_low_spread', 'high_low_spread_pct']
volume_analysis = ['volume_sma_30', 'volume_ratio']
support_resistance = ['support_level', 'resistance_level', 'support_distance', 'resistance_distance']

print("🔹 Core OHLCV Data (9 columns):")
for col in core_ohlcv:
    if col in df.columns:
        print(f"   • {col}")

print("\n🔹 Performance Metrics (6 columns):")
for col in performance:
    if col in df.columns:
        print(f"   • {col}")

print("\n🔹 Technical Indicators (9 columns):")
for col in technical:
    if col in df.columns:
        print(f"   • {col}")

print("\n🔹 Bollinger Bands (5 columns):")
for col in bollinger:
    if col in df.columns:
        print(f"   • {col}")

print("\n🔹 Risk & Volatility (4 columns):")
for col in risk_volatility:
    if col in df.columns:
        print(f"   • {col}")

print("\n🔹 Volume Analysis (2 columns):")
for col in volume_analysis:
    if col in df.columns:
        print(f"   • {col}")

print("\n🔹 Support/Resistance (4 columns):")
for col in support_resistance:
    if col in df.columns:
        print(f"   • {col}")

print(f"\n📋 SAMPLE DATA FOR HNT:")
print("-" * 60)
hnt_sample = df[df['symbol'] == 'HNT'].tail(5)
print("Recent 5 days of HNT data (showing key metrics):")
key_cols = ['date', 'open', 'high', 'low', 'close', 'volume_usd', 'daily_return', 'rsi_14', 'volatility_30d']
print(hnt_sample[key_cols].round(4))

print(f"\n📊 DATA QUALITY CHECK:")
print("-" * 60)
print("Records per token:")
print(df['symbol'].value_counts().sort_index())

print(f"\nMissing data summary:")
missing_pct = (df.isnull().sum() / len(df) * 100).round(2)
print("Columns with missing data:")
for col, pct in missing_pct[missing_pct > 0].items():
    print(f"   • {col}: {pct}% missing")

print(f"\n✅ Dataset is ready for comprehensive HNT analysis!")
print("   • 39 metrics per token")
print("   • 3 years of daily data")
print("   • Technical indicators calculated")
print("   • Performance metrics included")
print("   • Risk measures computed")
