import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Load data
df = pd.read_csv('comprehensive_crypto_data.csv')
df['date'] = pd.to_datetime(df['date'])

# Set up plotting style
plt.style.use('default')
sns.set_palette("husl")
plt.rcParams['figure.figsize'] = (15, 10)

# Create pivot tables
price_pivot = df.pivot(index='date', columns='symbol', values='close')
volume_pivot = df.pivot(index='date', columns='symbol', values='volume_usd')
return_pivot = df.pivot(index='date', columns='symbol', values='daily_return')

# 1. HNT Price Performance vs Major Cryptos
fig, axes = plt.subplots(2, 2, figsize=(20, 15))
fig.suptitle('HNT Comprehensive Analysis Dashboard', fontsize=16, fontweight='bold')

# Normalize prices to start at 100 for comparison
normalized_prices = price_pivot[['HNT', 'BTC', 'ETH', 'SOL', 'AVAX']].div(price_pivot[['HNT', 'BTC', 'ETH', 'SOL', 'AVAX']].iloc[0]) * 100

axes[0,0].plot(normalized_prices.index, normalized_prices['HNT'], linewidth=3, label='HNT', color='red')
axes[0,0].plot(normalized_prices.index, normalized_prices['BTC'], linewidth=2, label='BTC', alpha=0.7)
axes[0,0].plot(normalized_prices.index, normalized_prices['ETH'], linewidth=2, label='ETH', alpha=0.7)
axes[0,0].plot(normalized_prices.index, normalized_prices['SOL'], linewidth=2, label='SOL', alpha=0.7)
axes[0,0].plot(normalized_prices.index, normalized_prices['AVAX'], linewidth=2, label='AVAX', alpha=0.7)
axes[0,0].set_title('Price Performance Comparison (Normalized to 100)', fontweight='bold')
axes[0,0].set_ylabel('Normalized Price')
axes[0,0].legend()
axes[0,0].grid(True, alpha=0.3)

# 2. HNT Volume Analysis
hnt_data = df[df['symbol'] == 'HNT'].copy()
hnt_data['volume_ma_30'] = hnt_data['volume_usd'].rolling(30).mean()

axes[0,1].bar(hnt_data['date'], hnt_data['volume_usd'], alpha=0.6, color='lightblue', label='Daily Volume')
axes[0,1].plot(hnt_data['date'], hnt_data['volume_ma_30'], color='red', linewidth=2, label='30-day MA')
axes[0,1].set_title('HNT Trading Volume Over Time', fontweight='bold')
axes[0,1].set_ylabel('Volume (USD)')
axes[0,1].legend()
axes[0,1].grid(True, alpha=0.3)

# 3. Correlation Heatmap
correlation_matrix = return_pivot.corr()
mask = np.triu(np.ones_like(correlation_matrix, dtype=bool))
sns.heatmap(correlation_matrix, mask=mask, annot=True, cmap='RdYlBu_r', center=0,
            square=True, ax=axes[1,0], cbar_kws={"shrink": .8})
axes[1,0].set_title('Daily Returns Correlation Matrix', fontweight='bold')

# 4. HNT Technical Indicators
axes[1,1].plot(hnt_data['date'], hnt_data['close'], linewidth=2, label='HNT Price', color='black')
axes[1,1].plot(hnt_data['date'], hnt_data['sma_30'], linewidth=1, label='SMA-30', color='blue', alpha=0.7)
axes[1,1].plot(hnt_data['date'], hnt_data['sma_90'], linewidth=1, label='SMA-90', color='red', alpha=0.7)
axes[1,1].set_title('HNT Price with Moving Averages', fontweight='bold')
axes[1,1].set_ylabel('Price (USD)')
axes[1,1].legend()
axes[1,1].grid(True, alpha=0.3)

plt.tight_layout()
plt.savefig('hnt_analysis_dashboard.png', dpi=300, bbox_inches='tight')
plt.show()

# 5. Create additional focused charts
fig, axes = plt.subplots(2, 2, figsize=(20, 15))
fig.suptitle('HNT Detailed Technical Analysis', fontsize=16, fontweight='bold')

# RSI Chart
axes[0,0].plot(hnt_data['date'], hnt_data['rsi_14'], linewidth=2, color='purple')
axes[0,0].axhline(y=70, color='red', linestyle='--', alpha=0.7, label='Overbought (70)')
axes[0,0].axhline(y=30, color='green', linestyle='--', alpha=0.7, label='Oversold (30)')
axes[0,0].fill_between(hnt_data['date'], 30, 70, alpha=0.1, color='gray', label='Neutral Zone')
axes[0,0].set_title('HNT RSI (14-day)', fontweight='bold')
axes[0,0].set_ylabel('RSI')
axes[0,0].legend()
axes[0,0].grid(True, alpha=0.3)

# MACD Chart
axes[0,1].plot(hnt_data['date'], hnt_data['macd'], linewidth=2, label='MACD', color='blue')
axes[0,1].plot(hnt_data['date'], hnt_data['macd_signal'], linewidth=2, label='Signal', color='red')
axes[0,1].bar(hnt_data['date'], hnt_data['macd_histogram'], alpha=0.6, label='Histogram', color='gray')
axes[0,1].set_title('HNT MACD Indicator', fontweight='bold')
axes[0,1].legend()
axes[0,1].grid(True, alpha=0.3)

# Volatility Chart
hnt_data['volatility_30d'] = hnt_data['daily_return'].rolling(30).std() * np.sqrt(365)
axes[1,0].plot(hnt_data['date'], hnt_data['volatility_30d'], linewidth=2, color='orange')
axes[1,0].set_title('HNT 30-day Rolling Volatility', fontweight='bold')
axes[1,0].set_ylabel('Annualized Volatility (%)')
axes[1,0].grid(True, alpha=0.3)

# Returns Distribution
axes[1,1].hist(hnt_data['daily_return'].dropna(), bins=50, alpha=0.7, color='lightgreen', edgecolor='black')
axes[1,1].axvline(hnt_data['daily_return'].mean(), color='red', linestyle='--', linewidth=2, label=f'Mean: {hnt_data["daily_return"].mean():.2f}%')
axes[1,1].set_title('HNT Daily Returns Distribution', fontweight='bold')
axes[1,1].set_xlabel('Daily Return (%)')
axes[1,1].set_ylabel('Frequency')
axes[1,1].legend()
axes[1,1].grid(True, alpha=0.3)

plt.tight_layout()
plt.savefig('hnt_technical_analysis.png', dpi=300, bbox_inches='tight')
plt.show()

print("Visualizations created and saved:")
print("1. hnt_analysis_dashboard.png - Main dashboard with price comparison, volume, correlations")
print("2. hnt_technical_analysis.png - Technical indicators (RSI, MACD, volatility, returns distribution)")
