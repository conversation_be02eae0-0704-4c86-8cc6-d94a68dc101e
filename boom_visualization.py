import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Load data
df = pd.read_csv('comprehensive_crypto_data.csv')
df['date'] = pd.to_datetime(df['date'])

# Create pivot tables
price_pivot = df.pivot(index='date', columns='symbol', values='close')
return_pivot = df.pivot(index='date', columns='symbol', values='daily_return')

# Set up plotting
plt.style.use('default')
fig, axes = plt.subplots(3, 2, figsize=(20, 18))
fig.suptitle('HNT vs Bitcoin: Boom and Crash Correspondence Analysis', fontsize=16, fontweight='bold')

# 1. Price comparison (normalized)
normalized_prices = price_pivot[['BTC', 'HNT']].div(price_pivot[['BTC', 'HNT']].iloc[0]) * 100

axes[0,0].plot(normalized_prices.index, normalized_prices['BTC'], linewidth=2, label='BTC', color='orange')
axes[0,0].plot(normalized_prices.index, normalized_prices['HNT'], linewidth=2, label='HNT', color='red')
axes[0,0].set_title('Normalized Price Performance (Base = 100)', fontweight='bold')
axes[0,0].set_ylabel('Normalized Price')
axes[0,0].legend()
axes[0,0].grid(True, alpha=0.3)

# Add boom/crash periods
btc_data = df[df['symbol'] == 'BTC'].copy().sort_values('date')
btc_data['return_30d'] = btc_data['close'].pct_change(30) * 100

boom_periods = btc_data[btc_data['return_30d'] > 30]['date']
crash_periods = btc_data[btc_data['return_30d'] < -15]['date']

for boom_date in boom_periods:
    axes[0,0].axvline(x=boom_date, color='green', alpha=0.3, linestyle='--')
for crash_date in crash_periods:
    axes[0,0].axvline(x=crash_date, color='red', alpha=0.3, linestyle='--')

# 2. Daily returns scatter plot
btc_returns = return_pivot['BTC'].dropna()
hnt_returns = return_pivot['HNT'].dropna()
common_dates = btc_returns.index.intersection(hnt_returns.index)
btc_common = btc_returns[common_dates]
hnt_common = hnt_returns[common_dates]

axes[0,1].scatter(btc_common, hnt_common, alpha=0.6, s=20)
axes[0,1].set_xlabel('BTC Daily Return (%)')
axes[0,1].set_ylabel('HNT Daily Return (%)')
axes[0,1].set_title('Daily Returns Correlation (BTC vs HNT)', fontweight='bold')

# Add correlation line
z = np.polyfit(btc_common, hnt_common, 1)
p = np.poly1d(z)
axes[0,1].plot(btc_common.sort_values(), p(btc_common.sort_values()), "r--", alpha=0.8)

correlation = btc_common.corr(hnt_common)
axes[0,1].text(0.05, 0.95, f'Correlation: {correlation:.3f}', transform=axes[0,1].transAxes, 
               bbox=dict(boxstyle="round", facecolor='wheat', alpha=0.8))
axes[0,1].grid(True, alpha=0.3)

# 3. Rolling correlation
rolling_corr_30 = btc_returns.rolling(30).corr(hnt_returns)
rolling_corr_90 = btc_returns.rolling(90).corr(hnt_returns)

axes[1,0].plot(rolling_corr_30.index, rolling_corr_30, linewidth=2, label='30-day', alpha=0.8)
axes[1,0].plot(rolling_corr_90.index, rolling_corr_90, linewidth=2, label='90-day', alpha=0.8)
axes[1,0].set_title('Rolling Correlation (BTC-HNT)', fontweight='bold')
axes[1,0].set_ylabel('Correlation')
axes[1,0].legend()
axes[1,0].grid(True, alpha=0.3)
axes[1,0].axhline(y=0, color='black', linestyle='-', alpha=0.3)

# 4. Extreme movements analysis
btc_extreme_up = btc_common > btc_common.quantile(0.95)
btc_extreme_down = btc_common < btc_common.quantile(0.05)

hnt_response_up = hnt_common[btc_extreme_up]
hnt_response_down = hnt_common[btc_extreme_down]
hnt_normal = hnt_common[~(btc_extreme_up | btc_extreme_down)]

response_data = [hnt_response_down, hnt_normal, hnt_response_up]
labels = ['BTC Extreme Down\n(<5th percentile)', 'BTC Normal\n(5th-95th percentile)', 'BTC Extreme Up\n(>95th percentile)']

axes[1,1].boxplot(response_data, labels=labels)
axes[1,1].set_title('HNT Response to BTC Extreme Movements', fontweight='bold')
axes[1,1].set_ylabel('HNT Daily Return (%)')
axes[1,1].grid(True, alpha=0.3)
axes[1,1].axhline(y=0, color='red', linestyle='--', alpha=0.5)

# 5. Cumulative returns during major periods
major_periods = [
    ("2023 Rally", "2023-01-01", "2023-04-30"),
    ("2024 Bull Run", "2024-01-01", "2024-06-30"),
    ("2024 Late Rally", "2024-10-01", "2024-12-31"),
    ("2025 YTD", "2025-01-01", "2025-10-08")
]

period_data = []
for period_name, start_date, end_date in major_periods:
    period_df = df[(df['date'] >= start_date) & (df['date'] <= end_date)]
    
    if len(period_df) > 0:
        btc_period = period_df[period_df['symbol'] == 'BTC']
        hnt_period = period_df[period_df['symbol'] == 'HNT']
        
        if len(btc_period) > 0 and len(hnt_period) > 0:
            btc_return = ((btc_period['close'].iloc[-1] / btc_period['close'].iloc[0]) - 1) * 100
            hnt_return = ((hnt_period['close'].iloc[-1] / hnt_period['close'].iloc[0]) - 1) * 100
            period_data.append([period_name, btc_return, hnt_return])

if period_data:
    period_df = pd.DataFrame(period_data, columns=['Period', 'BTC_Return', 'HNT_Return'])
    
    x = np.arange(len(period_df))
    width = 0.35
    
    axes[2,0].bar(x - width/2, period_df['BTC_Return'], width, label='BTC', alpha=0.8, color='orange')
    axes[2,0].bar(x + width/2, period_df['HNT_Return'], width, label='HNT', alpha=0.8, color='red')
    
    axes[2,0].set_xlabel('Period')
    axes[2,0].set_ylabel('Return (%)')
    axes[2,0].set_title('Period Returns Comparison', fontweight='bold')
    axes[2,0].set_xticks(x)
    axes[2,0].set_xticklabels(period_df['Period'], rotation=45)
    axes[2,0].legend()
    axes[2,0].grid(True, alpha=0.3)
    axes[2,0].axhline(y=0, color='black', linestyle='-', alpha=0.3)

# 6. Beta analysis over time
window = 90
rolling_beta = []
dates = []

for i in range(window, len(btc_common)):
    btc_window = btc_common.iloc[i-window:i]
    hnt_window = hnt_common.iloc[i-window:i]
    
    if len(btc_window) > 0 and len(hnt_window) > 0:
        beta = np.cov(hnt_window, btc_window)[0,1] / np.var(btc_window)
        rolling_beta.append(beta)
        dates.append(btc_common.index[i])

axes[2,1].plot(dates, rolling_beta, linewidth=2, color='purple')
axes[2,1].set_title('HNT Beta vs BTC (90-day Rolling)', fontweight='bold')
axes[2,1].set_ylabel('Beta')
axes[2,1].grid(True, alpha=0.3)
axes[2,1].axhline(y=1, color='red', linestyle='--', alpha=0.7, label='Beta = 1')
axes[2,1].axhline(y=0, color='black', linestyle='-', alpha=0.3)
axes[2,1].legend()

plt.tight_layout()
plt.savefig('hnt_btc_boom_analysis.png', dpi=300, bbox_inches='tight')
plt.show()

print("Boom/Crash correspondence visualization created: hnt_btc_boom_analysis.png")
