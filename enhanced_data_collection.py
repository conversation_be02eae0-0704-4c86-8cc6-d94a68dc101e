#!/usr/bin/env python3
"""
Enhanced Cryptocurrency Data Collection Script
Collects comprehensive OHLCV data and calculates technical indicators
"""

import requests
import pandas as pd
import numpy as np
import time
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

def get_comprehensive_data(symbol, days=1095):
    """
    Fetch comprehensive OHLCV data from CryptoCompare API
    """
    url = "https://min-api.cryptocompare.com/data/v2/histoday"
    params = {
        'fsym': symbol,
        'tsym': 'USD',
        'limit': min(days, 2000),
        'api_key': ''  # Free tier
    }
    
    try:
        response = requests.get(url, params=params, timeout=30)
        response.raise_for_status()
        data = response.json()
        
        if data.get('Response') == 'Success':
            return data['Data']['Data']
        else:
            print(f"API error for {symbol}: {data.get('Message', 'Unknown error')}")
            return None
    except requests.exceptions.RequestException as e:
        print(f"Error fetching data for {symbol}: {e}")
        return None

def calculate_rsi(prices, period=14):
    """Calculate Relative Strength Index"""
    delta = prices.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    return rsi

def calculate_bollinger_bands(prices, period=20, std_dev=2):
    """Calculate Bollinger Bands"""
    sma = prices.rolling(window=period).mean()
    std = prices.rolling(window=period).std()
    upper_band = sma + (std * std_dev)
    lower_band = sma - (std * std_dev)
    return upper_band, lower_band, sma

def process_comprehensive_data(raw_data, symbol):
    """
    Process raw OHLCV data into comprehensive dataset with technical indicators
    """
    if not raw_data:
        return None
    
    df_data = []
    for item in raw_data:
        df_data.append({
            'date': datetime.fromtimestamp(item['time']).strftime('%Y-%m-%d'),
            'symbol': symbol,
            'open': item['open'],
            'high': item['high'],
            'low': item['low'],
            'close': item['close'],
            'volume_token': item['volumefrom'],  # Volume in token units
            'volume_usd': item['volumeto'],      # Volume in USD
            'market_cap': None  # Will be calculated if possible
        })
    
    df = pd.DataFrame(df_data)
    df['date'] = pd.to_datetime(df['date'])
    df = df.sort_values('date').reset_index(drop=True)
    
    # Calculate additional metrics
    df['daily_return'] = df['close'].pct_change() * 100
    df['price_change'] = df['close'] - df['open']
    df['price_change_pct'] = ((df['close'] - df['open']) / df['open']) * 100
    df['high_low_spread'] = df['high'] - df['low']
    df['high_low_spread_pct'] = (df['high_low_spread'] / df['open']) * 100
    
    # Moving averages
    df['sma_7'] = df['close'].rolling(window=7).mean()
    df['sma_30'] = df['close'].rolling(window=30).mean()
    df['sma_90'] = df['close'].rolling(window=90).mean()
    
    # Exponential moving averages
    df['ema_12'] = df['close'].ewm(span=12).mean()
    df['ema_26'] = df['close'].ewm(span=26).mean()
    
    # MACD
    df['macd'] = df['ema_12'] - df['ema_26']
    df['macd_signal'] = df['macd'].ewm(span=9).mean()
    df['macd_histogram'] = df['macd'] - df['macd_signal']
    
    # RSI
    df['rsi_14'] = calculate_rsi(df['close'], 14)
    
    # Bollinger Bands
    bb_upper, bb_lower, bb_middle = calculate_bollinger_bands(df['close'])
    df['bb_upper'] = bb_upper
    df['bb_lower'] = bb_lower
    df['bb_middle'] = bb_middle
    df['bb_width'] = ((bb_upper - bb_lower) / bb_middle) * 100
    df['bb_position'] = ((df['close'] - bb_lower) / (bb_upper - bb_lower)) * 100
    
    # Volatility measures
    df['volatility_30d'] = df['daily_return'].rolling(window=30).std()
    df['volatility_90d'] = df['daily_return'].rolling(window=90).std()
    
    # Price momentum
    df['momentum_7d'] = ((df['close'] - df['close'].shift(7)) / df['close'].shift(7)) * 100
    df['momentum_30d'] = ((df['close'] - df['close'].shift(30)) / df['close'].shift(30)) * 100
    df['momentum_90d'] = ((df['close'] - df['close'].shift(90)) / df['close'].shift(90)) * 100
    
    # Volume indicators
    df['volume_sma_30'] = df['volume_usd'].rolling(window=30).mean()
    df['volume_ratio'] = df['volume_usd'] / df['volume_sma_30']
    
    # Support/Resistance levels (simplified)
    df['support_level'] = df['low'].rolling(window=30).min()
    df['resistance_level'] = df['high'].rolling(window=30).max()
    df['support_distance'] = ((df['close'] - df['support_level']) / df['close']) * 100
    df['resistance_distance'] = ((df['resistance_level'] - df['close']) / df['close']) * 100
    
    return df

def main():
    """
    Main function to collect comprehensive data for all tokens
    """
    symbols = ['BTC', 'ETH', 'SOL', 'MATIC', 'AVAX', 'HNT', 'FIL', 'RNDR', 'TAO', 'AKT', 'IOTX', 'THETA']
    
    print("Starting enhanced data collection...")
    print(f"Collecting comprehensive data for {len(symbols)} tokens")
    print("Metrics per token: ~35 columns including OHLCV, technical indicators, and performance metrics")
    
    all_data = []
    
    for symbol in symbols:
        print(f"\nProcessing {symbol}...")
        
        # Fetch raw data
        raw_data = get_comprehensive_data(symbol, days=1095)
        
        if raw_data:
            # Process into comprehensive dataset
            df = process_comprehensive_data(raw_data, symbol)
            
            if df is not None and len(df) > 0:
                all_data.append(df)
                print(f"✓ Collected {len(df)} days with {len(df.columns)} metrics for {symbol}")
                
                # Show sample of what we collected
                print(f"  Sample metrics: {list(df.columns[:10])}...")
            else:
                print(f"✗ Failed to process data for {symbol}")
        else:
            print(f"✗ Failed to fetch data for {symbol}")
        
        # Rate limiting
        time.sleep(3)
    
    if all_data:
        # Combine all data
        combined_df = pd.concat(all_data, ignore_index=True)
        combined_df = combined_df.sort_values(['date', 'symbol']).reset_index(drop=True)
        
        # Save comprehensive dataset
        output_file = 'comprehensive_crypto_data.csv'
        combined_df.to_csv(output_file, index=False)
        
        print(f"\n✅ COMPREHENSIVE DATA COLLECTION COMPLETE!")
        print(f"📊 Dataset saved to: {output_file}")
        print(f"📈 Total records: {len(combined_df):,}")
        print(f"🪙 Tokens: {len(combined_df['symbol'].unique())}")
        print(f"📅 Date range: {combined_df['date'].min()} to {combined_df['date'].max()}")
        print(f"📋 Metrics per token: {len(combined_df.columns)}")
        
        print(f"\n📊 COLUMN BREAKDOWN:")
        print("Core OHLCV: open, high, low, close, volume_token, volume_usd")
        print("Performance: daily_return, price_change, momentum (7d/30d/90d)")
        print("Technical: RSI, MACD, Bollinger Bands, Moving Averages")
        print("Risk: volatility (30d/90d), support/resistance levels")
        print("Volume: volume ratios and moving averages")
        
        # Create summary
        summary_stats = combined_df.groupby('symbol').agg({
            'close': ['first', 'last', 'min', 'max', 'mean'],
            'volume_usd': 'mean',
            'daily_return': ['mean', 'std'],
            'rsi_14': 'mean'
        }).round(4)
        
        summary_stats.to_csv('token_summary_stats.csv')
        print(f"📋 Summary statistics saved to: token_summary_stats.csv")
        
    else:
        print("❌ No data collected!")

if __name__ == "__main__":
    main()
