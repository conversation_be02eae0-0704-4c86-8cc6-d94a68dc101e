#!/usr/bin/env python3
"""
Enhanced HNT Comparison Analysis Framework
Comprehensive analysis using 39 metrics per token across 3 years of data
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Set up plotting style
plt.style.use('default')
sns.set_palette("husl")

class EnhancedHNTAnalyzer:
    def __init__(self, data_file='comprehensive_crypto_data.csv', tokens_file='tokens_dataset.csv'):
        """Initialize the analyzer with comprehensive crypto data"""
        print("🚀 Loading comprehensive cryptocurrency dataset...")
        self.data = pd.read_csv(data_file)
        self.token_metadata = pd.read_csv(tokens_file)
        self.data['date'] = pd.to_datetime(self.data['date'])

        # Create pivot tables for key metrics
        self.price_pivot = self.data.pivot(index='date', columns='symbol', values='close')
        self.volume_pivot = self.data.pivot(index='date', columns='symbol', values='volume_usd')
        self.returns_pivot = self.data.pivot(index='date', columns='symbol', values='daily_return')

        print(f"✅ Loaded {len(self.data):,} records for {len(self.price_pivot.columns)} tokens")
        print(f"📅 Date range: {self.data['date'].min().date()} to {self.data['date'].max().date()}")
        print(f"📊 Available metrics: {len(self.data.columns)} columns per token")
    
    def calculate_returns(self, period='daily'):
        """Calculate returns for all tokens"""
        if period == 'daily':
            returns = self.price_pivot.pct_change().dropna()
        elif period == 'weekly':
            weekly_prices = self.price_pivot.resample('W').last()
            returns = weekly_prices.pct_change().dropna()
        elif period == 'monthly':
            monthly_prices = self.price_pivot.resample('M').last()
            returns = monthly_prices.pct_change().dropna()
        
        return returns
    
    def calculate_comprehensive_metrics(self):
        """Calculate comprehensive performance metrics using all available data"""
        print("📊 Calculating comprehensive performance metrics...")

        metrics = {}
        for token in self.price_pivot.columns:
            token_data = self.data[self.data['symbol'] == token].copy()

            if len(token_data) > 0:
                # Price performance
                first_price = token_data['close'].iloc[0]
                last_price = token_data['close'].iloc[-1]
                total_return = ((last_price / first_price) - 1) * 100

                # Risk metrics
                daily_returns = token_data['daily_return'].dropna()
                volatility_annual = daily_returns.std() * np.sqrt(252)
                sharpe_ratio = (daily_returns.mean() * 252) / (volatility_annual * 100) if volatility_annual > 0 else 0

                # Technical indicators (latest values)
                latest_data = token_data.iloc[-1]

                # Volume analysis
                avg_volume = token_data['volume_usd'].mean()
                volume_trend = token_data['volume_usd'].iloc[-30:].mean() / token_data['volume_usd'].iloc[-90:-30].mean()

                # Momentum analysis
                momentum_7d = latest_data.get('momentum_7d', 0)
                momentum_30d = latest_data.get('momentum_30d', 0)
                momentum_90d = latest_data.get('momentum_90d', 0)

                # Technical strength
                rsi = latest_data.get('rsi_14', 50)
                bb_position = latest_data.get('bb_position', 50)

                # Support/Resistance analysis
                support_distance = latest_data.get('support_distance', 0)
                resistance_distance = latest_data.get('resistance_distance', 0)

                metrics[token] = {
                    'Current Price': round(last_price, 4),
                    'Total Return (%)': round(total_return, 2),
                    'Annualized Volatility (%)': round(volatility_annual * 100, 2),
                    'Sharpe Ratio': round(sharpe_ratio, 3),
                    'Average Volume (USD)': round(avg_volume, 0),
                    'Volume Trend': round(volume_trend, 2),
                    'Momentum 7D (%)': round(momentum_7d, 2),
                    'Momentum 30D (%)': round(momentum_30d, 2),
                    'Momentum 90D (%)': round(momentum_90d, 2),
                    'RSI (14-day)': round(rsi, 1),
                    'Bollinger Position (%)': round(bb_position, 1),
                    'Support Distance (%)': round(support_distance, 2),
                    'Resistance Distance (%)': round(resistance_distance, 2),
                    'Current Volatility (30D)': round(latest_data.get('volatility_30d', 0), 2)
                }

        return pd.DataFrame(metrics).T
    
    def calculate_max_drawdown(self, token):
        """Calculate maximum drawdown for a token"""
        prices = self.price_pivot[token].dropna()
        cumulative = (1 + prices.pct_change()).cumprod()
        running_max = cumulative.expanding().max()
        drawdown = (cumulative - running_max) / running_max
        return drawdown.min() * 100
    
    def hnt_vs_category_comparison(self):
        """Compare HNT performance against different categories"""
        metrics = self.calculate_performance_metrics()
        
        # Categorize tokens
        major_crypto = ['BTC', 'ETH', 'SOL', 'MATIC', 'AVAX']
        depin_tokens = ['HNT', 'FIL', 'RNDR', 'TAO', 'AKT', 'IOTX', 'THETA']
        
        print("=== HNT vs Major Cryptocurrencies ===")
        major_metrics = metrics.loc[major_crypto]
        hnt_metrics = metrics.loc['HNT']
        
        print(f"HNT Total Return: {hnt_metrics['Total Return (%)']}%")
        print(f"Major Crypto Average: {major_metrics['Total Return (%)'].mean():.2f}%")
        print(f"HNT Rank vs Major Crypto: {(major_metrics['Total Return (%)'] < hnt_metrics['Total Return (%)']).sum() + 1}/6")
        
        print("\n=== HNT vs DePIN Competitors ===")
        depin_metrics = metrics.loc[depin_tokens]
        print(f"HNT Total Return: {hnt_metrics['Total Return (%)']}%")
        print(f"DePIN Average: {depin_metrics['Total Return (%)'].mean():.2f}%")
        print(f"HNT Rank vs DePIN: {(depin_metrics['Total Return (%)'] < hnt_metrics['Total Return (%)']).sum() + 1}/7")
        
        return metrics
    
    def correlation_analysis(self):
        """Analyze correlations between HNT and other tokens"""
        returns = self.calculate_returns()
        correlations = returns.corr()['HNT'].sort_values(ascending=False)
        
        print("=== HNT Correlation Analysis ===")
        print("Most correlated tokens with HNT:")
        for token, corr in correlations.head(6).items():
            if token != 'HNT':
                print(f"{token}: {corr:.3f}")
        
        return correlations
    
    def generate_summary_report(self):
        """Generate a comprehensive summary report"""
        print("=" * 60)
        print("HNT COMPREHENSIVE ANALYSIS REPORT")
        print("=" * 60)
        
        # Performance metrics
        metrics = self.calculate_performance_metrics()
        print("\n📊 PERFORMANCE METRICS (3-Year Period)")
        print("-" * 40)
        print(metrics.round(3))
        
        # Category comparisons
        print("\n🏆 CATEGORY COMPARISONS")
        print("-" * 40)
        self.hnt_vs_category_comparison()
        
        # Correlation analysis
        print("\n🔗 CORRELATION ANALYSIS")
        print("-" * 40)
        correlations = self.correlation_analysis()
        
        # Key insights
        hnt_metrics = metrics.loc['HNT']
        print(f"\n💡 KEY INSIGHTS FOR HNT")
        print("-" * 40)
        print(f"• Total 3-year return: {hnt_metrics['Total Return (%)']}%")
        print(f"• Annualized return: {hnt_metrics['Annualized Return (%)']}%")
        print(f"• Risk (volatility): {hnt_metrics['Volatility (%)']}%")
        print(f"• Risk-adjusted return (Sharpe): {hnt_metrics['Sharpe Ratio']}")
        print(f"• Worst drawdown: {hnt_metrics['Max Drawdown (%)']}%")
        print(f"• Current price: ${hnt_metrics['Current Price']}")
        
        return metrics

def main():
    """Run the complete HNT analysis"""
    analyzer = HNTAnalyzer()
    
    # Generate comprehensive report
    metrics = analyzer.generate_summary_report()
    
    # Save results
    metrics.to_csv('hnt_performance_analysis.csv')
    print(f"\n✅ Analysis complete! Results saved to 'hnt_performance_analysis.csv'")

if __name__ == "__main__":
    main()
