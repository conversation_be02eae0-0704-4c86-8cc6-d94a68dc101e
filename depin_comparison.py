import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

# Load data
df = pd.read_csv('comprehensive_crypto_data.csv')
df['date'] = pd.to_datetime(df['date'])

# Define DePIN tokens
depin_tokens = ['HNT', 'IOTX', 'AKT', 'FIL', 'THETA', 'RNDR', 'TAO']
major_tokens = ['BTC', 'ETH', 'SOL', 'AVAX', 'MATIC']

print("=" * 100)
print("DEPIN TOKENS COMPARISON: HNT vs INFRASTRUCTURE COMPETITORS")
print("=" * 100)

# Create pivot tables for DePIN tokens
price_pivot = df.pivot(index='date', columns='symbol', values='close')
return_pivot = df.pivot(index='date', columns='symbol', values='daily_return')
volume_pivot = df.pivot(index='date', columns='symbol', values='volume_usd')

# 1. Performance Comparison
print("\n1. DEPIN TOKENS PERFORMANCE COMPARISON (3-Year Period)")
print("-" * 70)

depin_performance = {}
for token in depin_tokens:
    token_data = df[df['symbol'] == token]
    if len(token_data) > 0:
        start_price = token_data['close'].iloc[0]
        end_price = token_data['close'].iloc[-1]
        max_price = token_data['close'].max()
        min_price = token_data['close'].min()
        
        total_return = ((end_price - start_price) / start_price) * 100
        max_drawdown = ((min_price - max_price) / max_price) * 100
        volatility = token_data['daily_return'].std() * np.sqrt(365)
        avg_volume = token_data['volume_usd'].mean()
        
        depin_performance[token] = {
            'total_return': total_return,
            'max_drawdown': max_drawdown,
            'volatility': volatility,
            'avg_volume': avg_volume,
            'start_price': start_price,
            'end_price': end_price,
            'max_price': max_price
        }

# Sort by total return
sorted_performance = sorted(depin_performance.items(), key=lambda x: x[1]['total_return'], reverse=True)

print(f"{'Token':<6} {'Total Return':<12} {'Max Drawdown':<13} {'Volatility':<11} {'Avg Volume':<15}")
print("-" * 70)
for token, perf in sorted_performance:
    print(f"{token:<6} {perf['total_return']:>+10.1f}% {perf['max_drawdown']:>+11.1f}% {perf['volatility']:>9.1f}% ${perf['avg_volume']:>12,.0f}")

# Find HNT's ranking
hnt_rank = next(i for i, (token, _) in enumerate(sorted_performance, 1) if token == 'HNT')
print(f"\nHNT Ranking: {hnt_rank} out of {len(depin_tokens)} DePIN tokens")

# 2. Correlation Analysis within DePIN
print("\n2. HNT CORRELATION WITH OTHER DEPIN TOKENS")
print("-" * 50)

depin_correlations = return_pivot[depin_tokens].corr()['HNT'].sort_values(ascending=False)
print("Daily Returns Correlations with HNT:")
for token, corr in depin_correlations.items():
    if token != 'HNT' and not pd.isna(corr):
        strength = "Very Strong" if abs(corr) > 0.7 else "Strong" if abs(corr) > 0.5 else "Moderate" if abs(corr) > 0.3 else "Weak"
        print(f"  {token:<6}: {corr:>6.3f} ({strength})")

# 3. Risk-Adjusted Returns Comparison
print("\n3. RISK-ADJUSTED RETURNS (SHARPE RATIO)")
print("-" * 45)

sharpe_ratios = {}
for token in depin_tokens:
    token_returns = return_pivot[token].dropna()
    if len(token_returns) > 0:
        sharpe = token_returns.mean() / token_returns.std() * np.sqrt(365)
        sharpe_ratios[token] = sharpe

sorted_sharpe = sorted(sharpe_ratios.items(), key=lambda x: x[1], reverse=True)
print(f"{'Token':<6} {'Sharpe Ratio':<12} {'Interpretation'}")
print("-" * 45)
for token, sharpe in sorted_sharpe:
    if sharpe > 1:
        interpretation = "Excellent"
    elif sharpe > 0.5:
        interpretation = "Good"
    elif sharpe > 0:
        interpretation = "Acceptable"
    else:
        interpretation = "Poor"
    print(f"{token:<6} {sharpe:>10.3f}   {interpretation}")

hnt_sharpe_rank = next(i for i, (token, _) in enumerate(sorted_sharpe, 1) if token == 'HNT')
print(f"\nHNT Sharpe Ratio Ranking: {hnt_sharpe_rank} out of {len(depin_tokens)} DePIN tokens")

# 4. Volume and Liquidity Comparison
print("\n4. LIQUIDITY COMPARISON (AVERAGE DAILY VOLUME)")
print("-" * 55)

volume_comparison = [(token, perf['avg_volume']) for token, perf in depin_performance.items()]
volume_comparison.sort(key=lambda x: x[1], reverse=True)

print(f"{'Token':<6} {'Avg Daily Volume':<20} {'Liquidity Rating'}")
print("-" * 55)
for token, avg_vol in volume_comparison:
    if avg_vol > 5000000:
        rating = "Excellent"
    elif avg_vol > 1000000:
        rating = "Good"
    elif avg_vol > 500000:
        rating = "Moderate"
    else:
        rating = "Poor"
    print(f"{token:<6} ${avg_vol:>15,.0f}   {rating}")

hnt_volume_rank = next(i for i, (token, _) in enumerate(volume_comparison, 1) if token == 'HNT')
print(f"\nHNT Volume Ranking: {hnt_volume_rank} out of {len(depin_tokens)} DePIN tokens")

# 5. Current Technical Position
print("\n5. CURRENT TECHNICAL POSITION COMPARISON")
print("-" * 50)

latest_data = df[df['date'] == df['date'].max()]
print(f"{'Token':<6} {'Price':<10} {'RSI':<6} {'MACD Signal':<12}")
print("-" * 50)
for token in depin_tokens:
    token_latest = latest_data[latest_data['symbol'] == token]
    if len(token_latest) > 0:
        price = token_latest['close'].iloc[0]
        rsi = token_latest['rsi_14'].iloc[0]
        macd_hist = token_latest['macd_histogram'].iloc[0]
        
        macd_signal = "Bullish" if macd_hist > 0 else "Bearish"
        print(f"{token:<6} ${price:>7.4f} {rsi:>5.1f} {macd_signal:<12}")

# 6. Mining/Infrastructure Viability Assessment
print("\n6. DEPIN INFRASTRUCTURE VIABILITY ASSESSMENT")
print("-" * 55)

print("Scoring each DePIN token on key factors (0-100 scale):")
print("Factors: Performance (30%), Liquidity (25%), Risk-Adj Returns (25%), Technical (20%)")
print()

viability_scores = {}
for token in depin_tokens:
    score = 0
    
    # Performance score (30%)
    perf = depin_performance[token]
    if perf['total_return'] > 100:
        score += 30
    elif perf['total_return'] > 0:
        score += 20
    elif perf['total_return'] > -50:
        score += 10
    
    # Liquidity score (25%)
    if perf['avg_volume'] > 5000000:
        score += 25
    elif perf['avg_volume'] > 1000000:
        score += 20
    elif perf['avg_volume'] > 500000:
        score += 15
    elif perf['avg_volume'] > 100000:
        score += 10
    
    # Risk-adjusted returns (25%)
    token_sharpe = sharpe_ratios.get(token, 0)
    if token_sharpe > 1:
        score += 25
    elif token_sharpe > 0.5:
        score += 20
    elif token_sharpe > 0:
        score += 15
    elif token_sharpe > -0.5:
        score += 10
    
    # Technical position (20%)
    token_latest = latest_data[latest_data['symbol'] == token]
    if len(token_latest) > 0:
        rsi = token_latest['rsi_14'].iloc[0]
        macd_hist = token_latest['macd_histogram'].iloc[0]
        
        if 30 <= rsi <= 70 and macd_hist > 0:
            score += 20
        elif 30 <= rsi <= 70:
            score += 15
        elif macd_hist > 0:
            score += 10
        else:
            score += 5
    
    viability_scores[token] = score

# Sort by viability score
sorted_viability = sorted(viability_scores.items(), key=lambda x: x[1], reverse=True)

print(f"{'Token':<6} {'Score':<6} {'Viability Assessment'}")
print("-" * 55)
for token, score in sorted_viability:
    if score >= 80:
        assessment = "Highly Viable"
    elif score >= 60:
        assessment = "Viable"
    elif score >= 40:
        assessment = "Questionable"
    else:
        assessment = "Not Viable"
    print(f"{token:<6} {score:>4}/100 {assessment}")

hnt_viability_rank = next(i for i, (token, _) in enumerate(sorted_viability, 1) if token == 'HNT')
print(f"\nHNT Overall DePIN Viability Ranking: {hnt_viability_rank} out of {len(depin_tokens)} tokens")

print("\n" + "=" * 100)
print("DEPIN COMPARISON SUMMARY")
print("=" * 100)
print(f"HNT Performance vs Other DePIN Tokens:")
print(f"• Total Return Ranking: {hnt_rank}/{len(depin_tokens)}")
print(f"• Risk-Adjusted Return Ranking: {hnt_sharpe_rank}/{len(depin_tokens)}")
print(f"• Liquidity Ranking: {hnt_volume_rank}/{len(depin_tokens)}")
print(f"• Overall Viability Ranking: {hnt_viability_rank}/{len(depin_tokens)}")
print("=" * 100)
