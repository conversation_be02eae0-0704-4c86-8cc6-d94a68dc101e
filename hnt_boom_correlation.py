import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Load data
df = pd.read_csv('comprehensive_crypto_data.csv')
df['date'] = pd.to_datetime(df['date'])

# Create pivot tables
price_pivot = df.pivot(index='date', columns='symbol', values='close')
return_pivot = df.pivot(index='date', columns='symbol', values='daily_return')

print("=" * 100)
print("HNT CORRELATION WITH BITCOIN AND CRYPTO BOOMS/CRASHES")
print("=" * 100)

# 1. Identify Bitcoin boom and crash periods
print("\n1. IDENTIFYING BITCOIN BOOM AND CRASH PERIODS")
print("-" * 60)

# Calculate BTC rolling returns for different periods
btc_data = df[df['symbol'] == 'BTC'].copy().sort_values('date')
btc_data['return_7d'] = btc_data['close'].pct_change(7) * 100
btc_data['return_30d'] = btc_data['close'].pct_change(30) * 100
btc_data['return_90d'] = btc_data['close'].pct_change(90) * 100

# Define boom/crash thresholds
boom_threshold_7d = 15  # 15% gain in 7 days
boom_threshold_30d = 30  # 30% gain in 30 days
crash_threshold_7d = -15  # 15% loss in 7 days
crash_threshold_30d = -30  # 30% loss in 30 days

# Identify boom periods
btc_booms_7d = btc_data[btc_data['return_7d'] > boom_threshold_7d]
btc_booms_30d = btc_data[btc_data['return_30d'] > boom_threshold_30d]
btc_crashes_7d = btc_data[btc_data['return_7d'] < crash_threshold_7d]
btc_crashes_30d = btc_data[btc_data['return_30d'] < crash_threshold_30d]

print(f"Bitcoin Boom Periods Identified:")
print(f"  7-day booms (>{boom_threshold_7d}%): {len(btc_booms_7d)} periods")
print(f"  30-day booms (>{boom_threshold_30d}%): {len(btc_booms_30d)} periods")
print(f"\nBitcoin Crash Periods Identified:")
print(f"  7-day crashes (<{crash_threshold_7d}%): {len(btc_crashes_7d)} periods")
print(f"  30-day crashes (<{crash_threshold_30d}%): {len(btc_crashes_30d)} periods")

# 2. Analyze HNT behavior during BTC booms
print("\n2. HNT BEHAVIOR DURING BITCOIN BOOMS")
print("-" * 45)

def analyze_hnt_during_btc_events(btc_events, event_type, period):
    hnt_returns = []
    hnt_concurrent_returns = []
    
    for _, btc_event in btc_events.iterrows():
        event_date = btc_event['date']
        
        # Get HNT data for the same period
        hnt_event = df[(df['symbol'] == 'HNT') & (df['date'] == event_date)]
        if len(hnt_event) > 0:
            if period == '7d':
                hnt_return = hnt_event[f'momentum_7d'].iloc[0] if f'momentum_7d' in hnt_event.columns else None
            elif period == '30d':
                hnt_return = hnt_event[f'momentum_30d'].iloc[0] if f'momentum_30d' in hnt_event.columns else None
            
            if hnt_return is not None and not pd.isna(hnt_return):
                hnt_returns.append(hnt_return)
                hnt_concurrent_returns.append(hnt_event['daily_return'].iloc[0])
    
    return hnt_returns, hnt_concurrent_returns

# Analyze HNT during BTC 7-day booms
hnt_during_btc_booms_7d, hnt_concurrent_7d = analyze_hnt_during_btc_events(btc_booms_7d, 'boom', '7d')
hnt_during_btc_crashes_7d, hnt_concurrent_crash_7d = analyze_hnt_during_btc_events(btc_crashes_7d, 'crash', '7d')

if hnt_during_btc_booms_7d:
    print(f"HNT Performance During BTC 7-day Booms:")
    print(f"  Average HNT 7-day momentum: {np.mean(hnt_during_btc_booms_7d):.2f}%")
    print(f"  HNT positive periods: {sum(1 for x in hnt_during_btc_booms_7d if x > 0)}/{len(hnt_during_btc_booms_7d)} ({sum(1 for x in hnt_during_btc_booms_7d if x > 0)/len(hnt_during_btc_booms_7d)*100:.1f}%)")
    print(f"  Average concurrent daily return: {np.mean(hnt_concurrent_7d):.2f}%")

if hnt_during_btc_crashes_7d:
    print(f"\nHNT Performance During BTC 7-day Crashes:")
    print(f"  Average HNT 7-day momentum: {np.mean(hnt_during_btc_crashes_7d):.2f}%")
    print(f"  HNT positive periods: {sum(1 for x in hnt_during_btc_crashes_7d if x > 0)}/{len(hnt_during_btc_crashes_7d)} ({sum(1 for x in hnt_during_btc_crashes_7d if x > 0)/len(hnt_during_btc_crashes_7d)*100:.1f}%)")
    print(f"  Average concurrent daily return: {np.mean(hnt_concurrent_crash_7d):.2f}%")

# 3. Major Bitcoin price movements and HNT response
print("\n3. MAJOR BITCOIN MOVEMENTS AND HNT RESPONSE")
print("-" * 50)

# Find the biggest BTC moves
btc_data_clean = btc_data.dropna(subset=['daily_return'])
biggest_btc_gains = btc_data_clean.nlargest(10, 'daily_return')
biggest_btc_losses = btc_data_clean.nsmallest(10, 'daily_return')

print("Top 10 Bitcoin Daily Gains and HNT Response:")
print(f"{'Date':<12} {'BTC Return':<12} {'HNT Return':<12} {'HNT Response'}")
print("-" * 60)

for _, btc_day in biggest_btc_gains.iterrows():
    date = btc_day['date']
    btc_return = btc_day['daily_return']
    
    hnt_day = df[(df['symbol'] == 'HNT') & (df['date'] == date)]
    if len(hnt_day) > 0:
        hnt_return = hnt_day['daily_return'].iloc[0]
        if pd.notna(hnt_return):
            response = "Followed" if hnt_return > 0 else "Diverged"
            print(f"{date.strftime('%Y-%m-%d'):<12} {btc_return:>+10.2f}% {hnt_return:>+10.2f}% {response}")

print("\nTop 10 Bitcoin Daily Losses and HNT Response:")
print(f"{'Date':<12} {'BTC Return':<12} {'HNT Return':<12} {'HNT Response'}")
print("-" * 60)

for _, btc_day in biggest_btc_losses.iterrows():
    date = btc_day['date']
    btc_return = btc_day['daily_return']
    
    hnt_day = df[(df['symbol'] == 'HNT') & (df['date'] == date)]
    if len(hnt_day) > 0:
        hnt_return = hnt_day['daily_return'].iloc[0]
        if pd.notna(hnt_return):
            response = "Followed" if hnt_return < 0 else "Diverged"
            print(f"{date.strftime('%Y-%m-%d'):<12} {btc_return:>+10.2f}% {hnt_return:>+10.2f}% {response}")

# 4. Rolling correlation analysis
print("\n4. ROLLING CORRELATION ANALYSIS")
print("-" * 40)

# Calculate rolling correlations
window_sizes = [30, 90, 180]
for window in window_sizes:
    rolling_corr = return_pivot['BTC'].rolling(window).corr(return_pivot['HNT'])
    current_corr = rolling_corr.iloc[-1]
    avg_corr = rolling_corr.mean()
    max_corr = rolling_corr.max()
    min_corr = rolling_corr.min()
    
    print(f"{window}-day Rolling Correlation (BTC-HNT):")
    print(f"  Current: {current_corr:.3f}")
    print(f"  Average: {avg_corr:.3f}")
    print(f"  Range: {min_corr:.3f} to {max_corr:.3f}")
    print()

# 5. Specific boom periods analysis
print("5. ANALYSIS OF SPECIFIC CRYPTO BOOM PERIODS")
print("-" * 50)

# Define major crypto boom periods based on BTC price action
boom_periods = [
    ("2023 Rally", "2023-01-01", "2023-04-30"),
    ("2024 Bull Run", "2024-01-01", "2024-06-30"),
    ("2024 Late Rally", "2024-10-01", "2024-12-31"),
    ("2025 Performance", "2025-01-01", "2025-10-08")
]

for period_name, start_date, end_date in boom_periods:
    period_data = df[(df['date'] >= start_date) & (df['date'] <= end_date)]
    
    if len(period_data) > 0:
        btc_period = period_data[period_data['symbol'] == 'BTC']
        hnt_period = period_data[period_data['symbol'] == 'HNT']
        
        if len(btc_period) > 0 and len(hnt_period) > 0:
            btc_return = ((btc_period['close'].iloc[-1] / btc_period['close'].iloc[0]) - 1) * 100
            hnt_return = ((hnt_period['close'].iloc[-1] / hnt_period['close'].iloc[0]) - 1) * 100
            
            print(f"{period_name}:")
            print(f"  BTC Return: {btc_return:+.1f}%")
            print(f"  HNT Return: {hnt_return:+.1f}%")
            print(f"  HNT vs BTC: {hnt_return - btc_return:+.1f}% {'outperformance' if hnt_return > btc_return else 'underperformance'}")
            print()

# 6. Summary statistics
print("6. SUMMARY: HNT'S RELATIONSHIP WITH BITCOIN BOOMS")
print("-" * 55)

# Overall correlation
overall_corr = return_pivot['BTC'].corr(return_pivot['HNT'])
print(f"Overall BTC-HNT Daily Returns Correlation: {overall_corr:.3f}")

# Beta calculation (HNT sensitivity to BTC moves)
btc_returns = return_pivot['BTC'].dropna()
hnt_returns = return_pivot['HNT'].dropna()
common_dates = btc_returns.index.intersection(hnt_returns.index)
btc_common = btc_returns[common_dates]
hnt_common = hnt_returns[common_dates]

if len(btc_common) > 0 and len(hnt_common) > 0:
    beta = np.cov(hnt_common, btc_common)[0,1] / np.var(btc_common)
    print(f"HNT Beta vs BTC: {beta:.3f}")
    
    if beta > 1:
        print("  → HNT is MORE volatile than BTC (amplifies BTC moves)")
    elif beta > 0:
        print("  → HNT is LESS volatile than BTC but moves in same direction")
    else:
        print("  → HNT moves OPPOSITE to BTC")

# Conclusion
print(f"\nCONCLUSION:")
if overall_corr > 0.4:
    print("✓ HNT DOES correspond with Bitcoin booms and crashes")
    print("✓ Strong positive correlation suggests HNT follows BTC trends")
elif overall_corr > 0.2:
    print("⚠ HNT MODERATELY corresponds with Bitcoin movements")
    print("⚠ Some correlation but HNT has significant independent factors")
else:
    print("✗ HNT does NOT strongly correspond with Bitcoin movements")
    print("✗ HNT appears to move independently of Bitcoin trends")

print("=" * 100)
